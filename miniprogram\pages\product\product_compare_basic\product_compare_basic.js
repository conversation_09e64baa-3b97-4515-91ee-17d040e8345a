// pages/product/product_compare_basic/product_compare_basic.js
const api = require('../../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 加载状态
    loading: false,
    // 错误信息
    error: null,
    // 对比结果
    compareResult: null,
    // 展开状态控制
    expandedCategories: {},
    // 产品名称列表
    productNames: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('传统对比页面加载参数:', options);
    
    if (options.productNames) {
      try {
        const productNames = JSON.parse(decodeURIComponent(options.productNames));
        console.log('解析的产品名称:', productNames);
        
        this.setData({
          productNames: productNames
        });
        
        // 开始对比
        this.startCompare(productNames);
      } catch (error) {
        console.error('解析产品名称失败:', error);
        this.setData({
          error: '参数解析失败，请重新选择产品进行对比'
        });
      }
    } else {
      this.setData({
        error: '缺少产品参数，请返回重新选择产品'
      });
    }
  },

  /**
   * 开始产品对比
   */
  async startCompare(productNames) {
    if (!productNames || productNames.length < 2) {
      this.setData({
        error: '至少需要2个产品进行对比'
      });
      return;
    }

    this.setData({
      loading: true,
      error: null
    });

    try {
      console.log('调用传统对比API:', productNames);
      const result = await api.product.compareProductsBasic(productNames);
      console.log('传统对比结果:', result);

      if (result.success && result.data) {
        // 初始化展开状态 - 默认展开第一个分类
        const expandedCategories = {};
        const categories = Object.keys(result.data.rawComparisonData || {});
        if (categories.length > 0) {
          expandedCategories[categories[0]] = true;
        }

        this.setData({
          compareResult: result.data,
          expandedCategories: expandedCategories,
          loading: false
        });
      } else {
        throw new Error(result.message || '对比失败');
      }
    } catch (error) {
      console.error('产品对比失败:', error);
      this.setData({
        loading: false,
        error: error.message || '对比失败，请稍后重试'
      });
    }
  },

  /**
   * 切换分类展开状态
   */
  toggleCategory(e) {
    const { category } = e.currentTarget.dataset;
    const expandedCategories = { ...this.data.expandedCategories };
    expandedCategories[category] = !expandedCategories[category];
    
    this.setData({
      expandedCategories: expandedCategories
    });
  },

  /**
   * 重新加载
   */
  onRetry() {
    if (this.data.productNames.length > 0) {
      this.startCompare(this.data.productNames);
    }
  },

  /**
   * 复制对比结果
   */
  copyCompareResult() {
    if (!this.data.compareResult) {
      wx.showToast({
        title: '暂无对比数据',
        icon: 'none'
      });
      return;
    }

    try {
      // 构建复制文本
      let copyText = '产品对比结果\n\n';
      
      // 添加产品信息
      copyText += '对比产品：\n';
      this.data.compareResult.products.forEach((product, index) => {
        copyText += `${index + 1}. ${product.skuName}\n`;
      });
      copyText += '\n';

      // 添加对比数据
      const rawData = this.data.compareResult.rawComparisonData;
      Object.keys(rawData).forEach(category => {
        copyText += `【${category}】\n`;
        Object.keys(rawData[category]).forEach(spec => {
          copyText += `${spec}：\n`;
          Object.keys(rawData[category][spec]).forEach(productName => {
            copyText += `  ${productName}: ${rawData[category][spec][productName]}\n`;
          });
          copyText += '\n';
        });
        copyText += '\n';
      });

      wx.setClipboardData({
        data: copyText,
        success: () => {
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('复制失败:', error);
      wx.showToast({
        title: '复制失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分享对比结果
   */
  shareCompareResult() {
    if (!this.data.compareResult) {
      wx.showToast({
        title: '暂无对比数据',
        icon: 'none'
      });
      return;
    }

    const productNames = this.data.compareResult.products.map(p => p.skuName).join(' vs ');
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 预览产品图片
   */
  previewProductImage(e) {
    const { imageUrl } = e.currentTarget.dataset;
    if (imageUrl) {
      wx.previewImage({
        urls: [imageUrl],
        current: imageUrl
      });
    }
  },

  /**
   * 查看产品详情
   */
  viewProductDetail(e) {
    const { productName } = e.currentTarget.dataset;
    if (productName) {
      wx.navigateTo({
        url: `/pages/product/product_detail/product_detail?productName=${encodeURIComponent(productName)}`
      });
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const productNames = this.data.compareResult ? 
      this.data.compareResult.products.map(p => p.skuName).join(' vs ') : 
      '产品对比';
    
    return {
      title: `${productNames} - 传统对比`,
      path: `/pages/product/product_compare_basic/product_compare_basic?productNames=${encodeURIComponent(JSON.stringify(this.data.productNames))}`,
      imageUrl: ''
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const productNames = this.data.compareResult ?
      this.data.compareResult.products.map(p => p.skuName).join(' vs ') :
      '产品对比';

    return {
      title: `${productNames} - 传统对比`,
      query: `productNames=${encodeURIComponent(JSON.stringify(this.data.productNames))}`,
      imageUrl: ''
    };
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    if (this.data.productNames.length > 0) {
      this.startCompare(this.data.productNames).finally(() => {
        wx.stopPullDownRefresh();
      });
    } else {
      wx.stopPullDownRefresh();
    }
  }
});

<!--pages/product/product_compare_basic/product_compare_basic.wxml-->
<wxs module="utils">
  // 截取文本长度
  var truncateText = function(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };
  
  module.exports = {
    truncateText: truncateText
  };
</wxs>

<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在分析产品参数...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-message">{{error}}</text>
    <button class="retry-btn" bindtap="onRetry">重新加载</button>
  </view>

  <!-- 对比结果 -->
  <view wx:elif="{{compareResult}}" class="compare-result">
    
    <!-- 对比产品头部 -->
    <view class="products-header">
      <view class="header-title">
        <text class="title-icon">📊</text>
        <text class="title-text">传统对比</text>
      </view>
      
      <!-- 产品列表 -->
      <scroll-view class="products-scroll" scroll-x="{{true}}">
        <view class="products-list">
          <view 
            class="product-card"
            wx:for="{{compareResult.products}}"
            wx:key="skuName"
            data-product-name="{{item.skuName}}"
            bindtap="viewProductDetail"
          >
            <view class="product-image-container">
              <image 
                class="product-image" 
                src="{{item.imageUrl}}" 
                mode="aspectFit"
                data-image-url="{{item.imageUrl}}"
                bindtap="previewProductImage"
                lazy-load
              />
              <view class="image-placeholder" wx:if="{{!item.imageUrl}}">
                <text>暂无图片</text>
              </view>
            </view>
            <view class="product-name">{{utils.truncateText(item.skuName, 20)}}</view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 操作按钮 -->
      <view class="header-actions">
        <button class="action-btn copy-btn" bindtap="copyCompareResult">
          <text class="btn-icon">📋</text>
          <text class="btn-text">复制结果</text>
        </button>
        <button class="action-btn share-btn" bindtap="shareCompareResult">
          <text class="btn-icon">📤</text>
          <text class="btn-text">分享</text>
        </button>
      </view>
    </view>

    <!-- 参数对比内容 -->
    <view class="compare-content">
      <block
        wx:for="{{compareResult.rawComparisonData}}"
        wx:key="{{index}}"
        wx:for-item="categoryData"
        wx:for-index="categoryName"
      >
        <view class="category-section">
          <!-- 分类标题 -->
          <view
            class="category-header {{expandedCategories[categoryName] ? 'expanded' : ''}}"
            bindtap="toggleCategory"
            data-category="{{categoryName}}"
          >
            <text class="category-name">{{categoryName}}</text>
            <view class="category-toggle">
              <text class="toggle-icon">{{expandedCategories[categoryName] ? '▼' : '▶'}}</text>
            </view>
          </view>

          <!-- 分类内容 -->
          <view class="category-content {{expandedCategories[categoryName] ? 'show' : 'hide'}}">
            <block
              wx:for="{{categoryData}}"
              wx:key="{{index}}"
              wx:for-item="specData"
              wx:for-index="specName"
            >
              <view class="spec-item">
                <view class="spec-name">{{specName}}</view>

                <!-- 产品参数值对比 -->
                <view class="spec-values">
                  <view
                    class="value-item"
                    wx:for="{{compareResult.products}}"
                    wx:key="skuName"
                    wx:for-item="product"
                  >
                    <view class="product-label">{{utils.truncateText(product.skuName, 15)}}</view>
                    <view class="product-value">{{specData[product.skuName] || '暂无数据'}}</view>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>

    <!-- 数据信息 -->
    <view class="data-info-section">
      <view class="section-header">
        <text class="section-title">📋 对比信息</text>
      </view>
      <view class="data-info-content">
        <view class="info-item">
          <text class="info-label">对比版本:</text>
          <text class="info-value">{{compareResult.meta.version || '基础版'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">产品数量:</text>
          <text class="info-value">{{compareResult.meta.foundProductsCount || 0}}个</text>
        </view>
        <view class="info-item">
          <text class="info-label">对比说明:</text>
          <text class="info-value">{{compareResult.meta.description || '基础产品参数对比'}}</text>
        </view>
        <view class="info-item" wx:if="{{compareResult.notFoundProducts && compareResult.notFoundProducts.length > 0}}">
          <text class="info-label">未找到产品:</text>
          <text class="info-value">{{compareResult.notFoundProducts.join(', ')}}</text>
        </view>
      </view>
    </view>

  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-container">
    <view class="empty-icon">📱</view>
    <view class="empty-title">暂无对比数据</view>
    <view class="empty-desc">请返回产品库选择产品进行对比</view>
  </view>
</view>

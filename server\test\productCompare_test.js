/**
 * ProductService V4 版本简化测试脚本
 * 测试基于参数字段提取的智能产品对比功能 - JSON格式输出
 * 功能: 从productService_v4获取AI分析结果并保存原始JSON数据到文件
 * 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro
 * 重点测试: 获取原始JSON数据，不进行额外的参数检测和数据处理
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { compareProductsByNamesV4 } = require('../src/services/product/productCompare');

// 数据库连接配置
const DB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDB() {
  try {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 断开数据库连接失败:', error.message);
  }
}



/**
 * 测试产品对比功能 - V4版本简化测试（仅保存原始JSON）
 */
async function testProductComparisonV4() {
  console.log('\n🆚 测试: 产品对比功能 V4 (华为 Mate 70 Pro vs 苹果iPhone 16 Pro)');
  console.log('='.repeat(80));

  const productNames = [
    '华为 Mate 70 Pro',       // 使用数据库中实际存在的产品名称
    '苹果iPhone 16 Pro'       // 使用数据库中实际存在的产品名称
  ];

  console.log(`📱 对比产品: ${productNames.join(' vs ')}`);
  console.log(`💡 注意: 简化版测试，仅获取和保存原始JSON数据`);

  try {
    const startTime = Date.now();

    const result = await compareProductsByNamesV4(productNames);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`⏱️ 对比耗时: ${duration}ms`);

    if (result.success) {
      console.log('\n✅ 产品对比成功!');

      // 保存原始JSON数据到文件
      try {
        const v4FileName = 'v4.json';
        const v4FilePath = path.join(__dirname, v4FileName);

        // 保存从productService_v4获取的完整原始数据
        fs.writeFileSync(v4FilePath, JSON.stringify(result, null, 2), 'utf8');
        console.log(`\n📄 V4版本原始JSON数据已保存到文件: ${v4FilePath}`);
        console.log(`📂 文件大小: ${fs.statSync(v4FilePath).size} 字节`);

        // 如果有AI分析结果，也单独保存结构化报告
        // if (result.data && result.data.aiAnalysis && result.data.aiAnalysis.structuredReport) {
        //   const reportFileName = 'v4_structured_report.json';
        //   const reportFilePath = path.join(__dirname, reportFileName);

        //   fs.writeFileSync(reportFilePath, JSON.stringify(result.data.aiAnalysis.structuredReport, null, 2), 'utf8');
        //   console.log(`📄 V4版本结构化报告已保存到文件: ${reportFilePath}`);
        //   console.log(`📂 报告文件大小: ${fs.statSync(reportFilePath).size} 字节`);
        // }

        console.log('\n✅ 原始JSON数据保存完成，无需进行额外的参数检测和数据处理');

      } catch (saveError) {
        console.error(`❌ 保存文件失败: ${saveError.message}`);
      }

    } else {
      console.log(`❌ 产品对比失败: ${result.error}`);
    }

  } catch (error) {
    console.log(`❌ 产品对比失败: ${error.message}`);
    console.error('详细错误:', error.stack);
  }
}

/**
 * 测试错误处理功能
 */
async function testErrorHandling() {
  console.log('\n🚨 测试: 错误处理功能');
  console.log('='.repeat(80));

  // 测试1: 产品数量不足
  console.log('\n测试1: 产品数量不足');
  try {
    const result = await compareProductsByNamesV4(['华为 Mate 70 Pro']);
    console.log(`   结果: ${result.success ? '成功' : '失败'}`);
    if (!result.success) {
      console.log(`   错误信息: ${result.error}`);
    }
  } catch (error) {
    console.log(`   异常: ${error.message}`);
  }

  // 测试2: 不存在的产品
  console.log('\n测试2: 不存在的产品');
  try {
    const result = await compareProductsByNamesV4(['不存在的产品A', '不存在的产品B']);
    console.log(`   结果: ${result.success ? '成功' : '失败'}`);
    if (!result.success) {
      console.log(`   错误信息: ${result.error}`);
    }
  } catch (error) {
    console.log(`   异常: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runAllTestsV4() {
  console.log('🚀 ProductService V4 简化测试开始');
  console.log('='.repeat(80));
  console.log(`测试时间: ${new Date().toLocaleString()}`);
  console.log(`Node.js版本: ${process.version}`);
  console.log(`测试重点: 获取和保存原始JSON数据`);

  try {
    // 连接数据库
    await connectDB();

    // 执行主要产品对比测试
    await testProductComparisonV4();

    // 执行错误处理测试
    await testErrorHandling();

    console.log('\n✅ 所有测试完成!');
    console.log('\n📊 测试总结:');
    console.log('   - 主要功能测试: V4产品对比功能');
    console.log('   - 原始数据保存: 保存完整的JSON响应');
    console.log('   - 结构化报告保存: 单独保存AI分析结果');
    console.log('   - 简化处理: 无额外的参数检测和数据处理');

  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    console.error('详细错误:', error.stack);
  } finally {
    // 断开数据库连接
    await disconnectDB();
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  // 检查环境变量
  console.log('🔧 环境检查:');
  console.log(`   MongoDB URI: ${DB_URL}`);
  console.log(`   DEEPSEEK_API_KEY: ${process.env.DEEPSEEK_API_KEY ? '已设置' : '未设置'}`);

  if (!process.env.DEEPSEEK_API_KEY) {
    console.warn('⚠️ 警告: DEEPSEEK_API_KEY 环境变量未设置，AI 分析功能可能无法正常工作');
  }

  runAllTestsV4().catch(error => {
    console.error('测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testProductComparisonV4,
  testErrorHandling,
  runAllTestsV4
};

/**
 * ProductCompareBasic 基础产品对比测试脚本
 * 功能: 对比华为 Mate 70 Pro vs 苹果iPhone 16 Pro，保存原始数据
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { compareProductsByNamesBasic } = require('../src/services/product/productCompareBasic');

// 数据库连接配置
const DB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDB() {
  try {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 断开数据库连接失败:', error.message);
  }
}

/**
 * 测试基础产品对比功能 - 保存原始数据
 */
async function testBasicProductComparison() {
  console.log('🆚 测试: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro');
  console.log('='.repeat(60));

  const productNames = ['华为 Mate 70 Pro', '苹果iPhone 16 Pro'];

  try {
    const result = await compareProductsByNamesBasic(productNames);

    if (result.success) {
      console.log('✅ 对比成功!');

      // 保存原始数据
      const fileName = 'productCompareBasic_result.json';
      const filePath = path.join(__dirname, fileName);

      fs.writeFileSync(filePath, JSON.stringify(result, null, 2), 'utf8');
      console.log(`📄 原始数据已保存: ${filePath}`);
      console.log(`📂 文件大小: ${fs.statSync(filePath).size} 字节`);

    } else {
      console.log(`❌ 对比失败: ${result.error}`);
    }

  } catch (error) {
    console.log(`❌ 对比失败: ${error.message}`);
  }
}



/**
 * 主测试函数
 */
async function runTest() {
  console.log('🚀 基础产品对比测试');
  console.log(`测试时间: ${new Date().toLocaleString()}`);

  try {
    // 连接数据库
    await connectDB();

    // 执行测试
    await testBasicProductComparison();

    console.log('\n✅ 测试完成!');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 断开数据库连接
    await disconnectDB();
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  console.log('🔧 环境检查:');
  console.log(`   MongoDB URI: ${DB_URL}`);

  runTest().catch(error => {
    console.error('测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testBasicProductComparison,
  runTest
};

// components/product-compare-input/product-compare-input.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示对比面板
    visible: {
      type: Boolean,
      value: false
    },
    // 对比产品列表
    compareProducts: {
      type: Array,
      value: []
    },
    // 最大对比产品数量
    maxCompareCount: {
      type: Number,
      value: 4
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 组件内部状态
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换对比面板显示状态
     */
    togglePanel() {
      this.triggerEvent('toggleVisible', {
        visible: !this.properties.visible
      });
    },

    /**
     * 移除对比产品
     */
    removeProduct(e) {
      const { index } = e.currentTarget.dataset;
      this.triggerEvent('removeProduct', {
        index: index
      });
    },

    /**
     * 清空所有对比产品
     */
    clearAll() {
      wx.showModal({
        title: '确认清空',
        content: '确定要清空所有对比产品吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('clearAll');
          }
        }
      });
    },

    /**
     * 开始对比
     */
    startCompare() {
      if (this.properties.compareProducts.length < 2) {
        wx.showToast({
          title: '至少选择2个产品进行对比',
          icon: 'none'
        });
        return;
      }
      
      this.triggerEvent('startCompare', {
        products: this.properties.compareProducts
      });
    },

    /**
     * 开始V4版本对比
     */
    startCompareV4() {
      if (this.properties.compareProducts.length < 2) {
        wx.showToast({
          title: '至少选择2个产品进行对比',
          icon: 'none'
        });
        return;
      }
      
      if (this.properties.compareProducts.length > 6) {
        wx.showToast({
          title: 'V4对比最多支持6个产品',
          icon: 'none'
        });
        return;
      }
      
      this.triggerEvent('startCompareV4', {
        products: this.properties.compareProducts
      });
    },

    /**
     * 产品点击事件
     */
    onProductTap(e) {
      const { product } = e.currentTarget.dataset;
      this.triggerEvent('productTap', {
        product: product
      });
    },

    /**
     * 图片加载错误处理
     */
    onImageError(e) {
      console.log('对比产品图片加载失败:', e);
    }
  }
});
<!--pages/product/product_library/product_library.wxml-->
<view class="container p-20">
  <!-- 筛选区域 -->
  <view class="filter-section card mb-20">
    <view class="card-header">
      <view class="filter-title card-title">产品筛选</view>
    </view>
    
    <view class="card-body">
      <!-- 产品类型选择 -->
      <view class="filter-row mb-30">
        <view class="filter-label form-label">产品类型</view>
        <view class="filter-options mb-10">
          <view 
            class="option-item {{item.value === selectedProductType ? 'selected' : ''}}"
            wx:for="{{productTypeOptions}}" 
            wx:key="value"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="onProductTypeSelect"
          >
            {{item.label}}
          </view>
        </view>
      </view>

      <!-- 品牌选择 -->
      <view class="filter-row mb-30">
        <view class="filter-label form-label">品牌</view>
        <view class="filter-options {{brandOptions.length === 0 ? 'disabled' : ''}} mb-10">
          <view 
            class="option-item {{item.value === selectedBrand ? 'selected' : ''}}"
            wx:for="{{brandOptions}}" 
            wx:key="value"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="onBrandSelect"
          >
            {{item.label}}
          </view>
          <view class="no-options" wx:if="{{brandOptions.length === 0}}">
            请先选择产品类型
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="filter-buttons mt-30">
        <button class="btn btn-primary btn-medium btn-full" bindtap="searchProducts" disabled="{{loading}}">
          {{loading ? '搜索中...' : '搜索产品'}}
        </button>
        <button class="btn btn-secondary btn-medium btn-full" bindtap="resetFilters">重置筛选</button>
      </view>
    </view>
  </view>

  <!-- 产品列表区域 -->
  <view class="products-section card">
    <view class="card-header">
      <view class="products-header mb-30 pb-20">
        <view class="products-title card-title">产品列表</view>
        <view class="products-count" wx:if="{{products.length > 0 || totalCount > 0}}">
          共{{totalCount || 0}}个产品
        </view>
      </view>
    </view>

    <view class="card-body">
      <!-- 使用产品展示组件 -->
      <product-show
        products="{{products}}"
        loading="{{loading}}"
        loadingMore="{{loadingMore}}"
        hasSearched="{{hasSearched}}"
        totalCount="{{totalCount}}"
        hasMore="{{hasMore}}"
        scrollTop="{{scrollTop}}"
        detailPagePath="/pages/product/product_detail/product_detail"
        bind:productTap="onProductTap"
        bind:imageError="onImageError"
        bind:loadMore="onLoadMoreClick"
        bind:scrollToLower="onScrollToLower"
        bind:compareToggle="onCompareToggle"
      ></product-show>
    </view>
  </view>

  <!-- 产品对比输入组件 -->
  <product-compare-input
    visible="{{compareVisible}}"
    compareProducts="{{compareProducts}}"
    maxCompareCount="{{6}}"
    bind:toggleVisible="onToggleCompareVisible"
    bind:removeProduct="onRemoveCompareProduct"
    bind:clearAll="onClearAllCompareProducts"
    bind:startCompare="onStartCompare"
    bind:startCompareV4="onStartCompareV4"
    bind:productTap="onCompareProductTap"
  ></product-compare-input>
</view>

/**
 * API服务模块
 */
const { post, get, del, patch, put, upload } = require('./request');

/**
 * 认证相关API
 */
const auth = {
  /**
   * 发送验证码
   * @param {string} phone 手机号
   * @param {string} purpose 验证码类型
   * @returns {Promise} Promise对象
   */
  sendVerifyCode: (phone, purpose) => {
    return post('/auth/verify-code', { phone, purpose });
  },

  /**
   * 手机号统一认证（自动判断登录/注册）
   * @param {string} phone 手机号
   * @param {string} verifyCode 验证码
   * @returns {Promise} Promise对象
   */
  phoneAuth: (phone, verifyCode) => {
    return post('/auth/phone-auth', { phone, verifyCode });
  },

  /**
   * 微信小程序登录
   * @param {string} code 微信临时登录凭证
   * @returns {Promise} Promise对象
   */
  wxLogin: (code) => {
    console.log('微信登录请求数据:', { code });
    return post('/auth/wx-login', { code });
  },

  /**
   * 刷新令牌
   * @param {string} refreshToken 刷新令牌
   * @returns {Promise} Promise对象
   */
  refreshToken: (refreshToken) => {
    return post('/auth/refresh-token', { refreshToken });
  },

  /**
   * 退出登录
   * @returns {Promise} Promise对象
   */
  logout: () => {
    return post('/auth/logout', {}, true);
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} Promise对象
   */
  getCurrentUser: () => {
    return get('/auth/me', {}, true);
  }
};

/**
 * 用户相关API
 */
const user = {
  /**
   * 获取用户参与投票的问题列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getVotedQuestions: (params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 10,
      sortBy: 'newest'
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    
    // 移除空值
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete queryParams[key];
      }
    });
    
    return get('/users/me/voted-questions', queryParams, true);
  },

  /**
   * 获取用户发起的问题列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getCreatedQuestions: (params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 10,
      sortBy: 'newest'
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    
    // 移除空值
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete queryParams[key];
      }
    });
    
    return get('/users/me/created-questions', queryParams, true);
  },

  /**
   * 获取用户已完成的问题列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getClosedQuestions: (params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 10,
      sortBy: 'newest',
      status: 'closed'
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    
    // 移除空值
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete queryParams[key];
      }
    });
    
    console.log('获取已完成问题列表参数:', queryParams);
    return get('/users/me/created-questions', queryParams, true);
  },

  /**
   * 更新用户基本资料
   * @param {Object} data 更新的用户资料数据
   * @param {String} data.nickname 昵称，可选，2-20个字符
   * @param {String} data.avatar 头像URL，可选
   * @param {String} data.gender 性别，可选，取值: 'male', 'female', 'secret'
   * @param {Number} data.age 年龄，可选，0-120
   * @param {String} data.occupation 职业，可选，最多50个字符
   * @param {String} data.region 地区，可选，最多50个字符
   * @returns {Promise} Promise对象
   */
  updateProfile: (data) => {
    // 参数验证
    if (!data || typeof data !== 'object') {
      return Promise.reject(new Error('用户资料数据不能为空'));
    }

    // 过滤掉空值和无效字段，构建更新数据
    const updateData = {};
    const allowedFields = ['nickname', 'avatar', 'gender', 'age', 'occupation', 'region'];
    
    allowedFields.forEach(field => {
      if (data[field] !== undefined && data[field] !== null) {
        // 对于字符串字段，去除前后空格
        if (typeof data[field] === 'string') {
          const trimmedValue = data[field].trim();
          if (trimmedValue !== '' || field === 'avatar' || field === 'occupation' || field === 'region') {
            updateData[field] = trimmedValue;
          }
        } else {
          updateData[field] = data[field];
        }
      }
    });

    // 检查是否有要更新的字段
    if (Object.keys(updateData).length === 0) {
      return Promise.reject(new Error('没有提供要更新的字段'));
    }

    // 前端基础验证
    if (updateData.nickname !== undefined) {
      if (updateData.nickname.length < 2 || updateData.nickname.length > 20) {
        return Promise.reject(new Error('昵称长度应在2-20个字符之间'));
      }
    }

    if (updateData.gender !== undefined) {
      if (!['male', 'female', 'secret'].includes(updateData.gender)) {
        return Promise.reject(new Error('性别值无效'));
      }
    }

    if (updateData.age !== undefined) {
      const age = Number(updateData.age);
      if (isNaN(age) || age < 0 || age > 120) {
        return Promise.reject(new Error('年龄应在0-120之间'));
      }
      updateData.age = age;
    }

    if (updateData.occupation !== undefined && updateData.occupation.length > 50) {
      return Promise.reject(new Error('职业描述不能超过50个字符'));
    }

    if (updateData.region !== undefined && updateData.region.length > 50) {
      return Promise.reject(new Error('地区信息不能超过50个字符'));
    }

    console.log('更新用户资料请求数据:', updateData);
    return put('/users/me/profile', updateData, true);
  },

  /**
   * 发送修改手机号验证码
   * @param {String} newPhone 新手机号
   * @returns {Promise} Promise对象
   */
  sendChangePhoneCode: (newPhone) => {
    // 参数验证
    if (!newPhone || typeof newPhone !== 'string') {
      return Promise.reject(new Error('新手机号不能为空'));
    }

    const trimmedPhone = newPhone.trim();
    if (!trimmedPhone) {
      return Promise.reject(new Error('新手机号不能为空'));
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(trimmedPhone)) {
      return Promise.reject(new Error('请输入有效的手机号'));
    }

    // 构建请求数据
    const requestData = {
      newPhone: trimmedPhone
    };

    console.log('发送修改手机号验证码请求数据:', requestData);
    return post('/users/me/change-phone/send-code', requestData, true);
  },

  /**
   * 修改手机号
   * @param {String} newPhone 新手机号
   * @param {String} verifyCode 验证码
   * @returns {Promise} Promise对象
   */
  changePhone: (newPhone, verifyCode) => {
    // 参数验证
    if (!newPhone || typeof newPhone !== 'string') {
      return Promise.reject(new Error('新手机号不能为空'));
    }

    if (!verifyCode || typeof verifyCode !== 'string') {
      return Promise.reject(new Error('验证码不能为空'));
    }

    const trimmedPhone = newPhone.trim();
    const trimmedCode = verifyCode.trim();

    if (!trimmedPhone) {
      return Promise.reject(new Error('新手机号不能为空'));
    }

    if (!trimmedCode) {
      return Promise.reject(new Error('验证码不能为空'));
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(trimmedPhone)) {
      return Promise.reject(new Error('请输入有效的手机号'));
    }

    // 验证验证码格式
    if (trimmedCode.length !== 6 || !/^\d{6}$/.test(trimmedCode)) {
      return Promise.reject(new Error('验证码必须是6位数字'));
    }

    // 构建请求数据
    const requestData = {
      newPhone: trimmedPhone,
      verifyCode: trimmedCode
    };

    console.log('修改手机号请求数据:', requestData);
    return put('/users/me/change-phone', requestData, true);
  }
};

/**
 * 问题相关API
 */
const question = {
  /**
   * 创建问题
   * @param {Object} data 问题数据
   * @returns {Promise} Promise对象
   */
  createQuestion: (data) => {
    return post('/community/questions', data, true);
  },

  /**
   * 获取问题列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getQuestions: (params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 10,
      status: 'open'
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    // 移除空值
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete queryParams[key];
      }
    });

    console.log("获取问题列表参数",queryParams);
    
    return get('/community/questions', queryParams, false);
  },

  /**
   * 获取问题详情
   * @param {string} id 问题ID
   * @returns {Promise} Promise对象
   */
  getQuestionById: (id) => {
    // return get(`/community/questions/${id}`, {}, true);
    return get(`/community/questions/${id}`, {}, false);
  },

  /**
   * 更新问题
   * @param {string} id 问题ID
   * @param {Object} data 更新数据
   * @returns {Promise} Promise对象
   */
  updateQuestion: (id, data) => {
    return post(`/community/questions/${id}`, data, true);
  },

  /**
   * 删除问题
   * @param {string} id 问题ID
   * @returns {Promise} Promise对象
   */
  deleteQuestion: (id) => {
    return del(`/community/questions/${id}`, {}, true);
  },

  /**
   * 关闭问题
   * @param {string} id 问题ID
   * @returns {Promise} Promise对象
   */
  closeQuestion: (id) => {
    return put(`/community/questions/${id}/close`, {}, true);
  },

  /**
   * 搜索问题
   * @param {Object} params 搜索参数
   * @param {string} params.keyword 搜索关键词
   * @param {string|Array} params.tags 标签过滤，可选值: "手机", "电脑"
   * @param {string} params.status 问题状态，可选值: "open", "closed"，默认为"open"
   * @param {number} params.page 页码，默认为1
   * @param {number} params.limit 每页数量，默认为10，范围1-50
   * @returns {Promise} Promise对象
   */
  searchQuestions: (params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 10,
      status: 'open'
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    
    // 移除空值，但保留空字符串的keyword（支持获取所有问题）
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined) {
        delete queryParams[key];
      }
      // 对于非keyword字段，移除空字符串
      if (key !== 'keyword' && queryParams[key] === '') {
        delete queryParams[key];
      }
    });

    console.log("搜索问题参数", queryParams);
    
    return get('/community/questions/search', queryParams, false);
  },

  /**
   * 根据问题ID对比问题下的产品选项
   * @param {string} id 问题ID
   * @returns {Promise} Promise对象
   */
  compareQuestionProducts: (id) => {
    // 参数验证
    if (!id || typeof id !== 'string') {
      return Promise.reject(new Error('问题ID不能为空'));
    }
    
    const trimmedId = id.trim();
    if (!trimmedId) {
      return Promise.reject(new Error('问题ID不能为空'));
    }
    
    console.log('请求问题产品对比，问题ID:', trimmedId);
    return post(`/community/questions/${trimmedId}/compare`, {}, true);
  },

  /**
   * AI推荐产品选项
   * @param {Object} questionInfo 问题信息
   * @param {string} questionInfo.title 问题标题
   * @param {string} questionInfo.scene 使用场景
   * @param {string} questionInfo.keyFactors 关键因素
   * @param {Object} filterOptions 筛选选项
   * @param {string} filterOptions.productType 产品类型，可选值: 'phone', 'laptop'
   * @param {Array<string>} filterOptions.brands 品牌列表
   * @param {Object} filterOptions.budget 预算范围，包含min和max字段
   * @param {Boolean} async 是否异步处理，默认为true
   * @returns {Promise} Promise对象
   */
  aiRecommendProducts: (questionInfo, filterOptions, async = true) => {
    // 参数验证
    if (!questionInfo || typeof questionInfo !== 'object') {
      return Promise.reject(new Error('问题信息不能为空'));
    }

    if (!filterOptions || typeof filterOptions !== 'object') {
      return Promise.reject(new Error('筛选选项不能为空'));
    }

    // 验证产品类型
    if (!filterOptions.productType || !['phone', 'laptop'].includes(filterOptions.productType)) {
      return Promise.reject(new Error('产品类型无效，请选择手机(phone)或笔记本(laptop)'));
    }

    // 验证筛选条件 - 必须选择品牌
    const hasBrands = filterOptions.brands && Array.isArray(filterOptions.brands) && filterOptions.brands.length > 0;
    
    if (!hasBrands) {
      return Promise.reject(new Error('筛选条件不足，请选择至少一个品牌'));
    }

    // 构建请求数据
    const requestData = {
      questionInfo: {
        title: questionInfo.title || '',
        scene: questionInfo.scene || '',
        keyFactors: questionInfo.keyFactors || ''
      },
      filterOptions: {
        productType: filterOptions.productType,
        brands: filterOptions.brands || [],
        budget: filterOptions.budget || null
      }
    };

    // 构建请求URL，如果是异步模式则添加async参数
    const url = async ? '/community/questions/ai-recommend?async=true' : '/community/questions/ai-recommend';

    console.log('AI推荐产品请求数据:', requestData);
    console.log('异步模式:', async);
    // 修改为不需要用户认证的公开接口
    return post(url, requestData, false);
  },

  /**
   * 检查AI推荐任务状态
   * @param {string} taskId 任务ID
   * @returns {Promise} Promise对象
   */
  checkAIRecommendStatus: (taskId) => {
    if (!taskId) {
      return Promise.reject(new Error('任务ID不能为空'));
    }
    // 修改为不需要用户认证的公开接口
    return get(`/community/questions/ai-recommend/status/${taskId}`, {}, false);
  }
};

/**
 * 回答相关API
 */
const answer = {
  /**
   * 提交回答
   * @param {string} questionId 问题ID
   * @param {Object} data 回答数据
   * @returns {Promise} Promise对象
   */
  createAnswer: (questionId, data) => {
    // 确保传递了必要的参数
    if (!data.optionId) {
      return Promise.reject(new Error('必须提供选项ID'));
    }
    
    // 构建请求数据
    const answerData = {
      optionId: data.optionId,
      content: data.content || '',
      isAnonymous: typeof data.isAnonymous === 'boolean' ? data.isAnonymous : false
    };
    
    // 提交回答需要用户认证
    return post(`/community/questions/${questionId}/answers`, answerData, true);
  },

  /**
   * 获取问题的回答列表
   * @param {string} questionId 问题ID
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getAnswers: (questionId, params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 10,
      sortBy: 'newest'
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    
    // 移除空值
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete queryParams[key];
      }
    });
    
    // 支持未登录用户访问
    return get(`/community/questions/${questionId}/answers`, queryParams, false);
  },

  /**
   * 点赞/取消点赞回答
   * @param {string} answerId 回答ID
   * @param {string} action 操作类型 (like/unlike)
   * @returns {Promise} Promise对象
   */
  likeAnswer: (answerId, action) => {
    return post(`/community/answers/${answerId}/like`, { action }, true);
  },

  /**
   * 获取回答信息
   * @param {string} answerId 回答ID
   * @returns {Promise} Promise对象
   */
  getAnswerInfo: (answerId) => {
    return get(`/community/answers/${answerId}`, {}, true);
  }
};

/**
 * 评论相关API
 */
const comment = {
  /**
   * 添加评论
   * @param {String} answerId 回答ID
   * @param {Object} data 评论数据
   * @returns {Promise} Promise对象
   */
  createComment: (answerId, data) => {
    console.log('提交评论数据:', data, data.parentId, answerId);
    return post(`/community/answers/${answerId}/comments`, data, true);
  },

  /**
   * 获取评论列表
   * @param {String} answerId 回答ID
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getComments: (answerId, params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 20
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    
    // 支持未登录用户查看评论
    return get(`/community/answers/${answerId}/comments`, queryParams, false);
  },

  /**
   * 获取评论回复
   * @param {String} commentId 评论ID
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getReplies: (commentId, params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 10,
      isTopLevel: false
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    
    // 支持未登录用户查看回复
    return get(`/community/comments/${commentId}/replies`, queryParams, false);
  },

  /**
   * 删除评论
   * @param {String} commentId 评论ID
   * @returns {Promise} Promise对象
   */
  deleteComment: (commentId) => {
    // 使用del方法
    return del(`/community/comments/${commentId}`, {}, true);
  },

  /**
   * 获取评论信息
   * @param {String} commentId 评论ID
   * @returns {Promise} Promise对象
   */
  getCommentInfo: (commentId) => {
    return get(`/community/comments/${commentId}`, {}, true);
  }
};

/**
 * 结果分析相关API
 */
const result = {
  /**
   * 获取问题的结果分析
   * @param {String} questionId 问题ID
   * @param {String} type 总结类型，可选值：rule, ai，默认为ai
   * @param {Boolean} async 是否异步处理，默认为false
   * @returns {Promise} Promise对象
   */
  getQuestionResult: (questionId, type = 'ai', async = false) => {
    // 检查参数有效性
    if (!questionId) {
      return Promise.reject(new Error('问题ID不能为空'));
    }
    
    // 构建查询参数
    const queryParams = {
      type,
      async
    };
    
    return get(`/community/questions/${questionId}/result`, queryParams, true);
  },
  
  /**
   * 检查问题结果分析状态
   * @param {String} questionId 问题ID
   * @param {String} type 总结类型，可选值：rule, ai，默认为ai
   * @returns {Promise} Promise对象
   */
  checkResultStatus: (questionId, type = 'ai') => {
    // 检查参数有效性
    if (!questionId) {
      return Promise.reject(new Error('问题ID不能为空'));
    }
    
    return get(`/community/questions/${questionId}/result/status`, { type }, true);
  }
};

/**
 * 通知相关API
 */
const notification = {
  /**
   * 获取通知列表
   * @param {Object} params 查询参数
   * @returns {Promise} Promise对象
   */
  getNotifications: (params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 20
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };

    console.log('获取通知列表，参数:', queryParams);
    const result = get('/notifications', queryParams, true);
    
    // 添加日志输出
    result.then(res => {
      console.log('通知API响应完整数据:', res);
      if (!res.success) {
        console.error('通知API请求失败:', res.message);
      }
    }).catch(err => {
      console.error('通知API请求异常:', err);
    });
    
    return result;
  },

  /**
   * 获取未读通知数量
   * @returns {Promise} Promise对象
   */
  getUnreadCount: () => {
    return get('/notifications/unread-count', {}, true);
  },

  /**
   * 标记单个通知为已读
   * @param {String} notificationId 通知ID
   * @returns {Promise} Promise对象
   */
  markAsRead: (notificationId) => {
    return patch(`/notifications/${notificationId}/mark-read`, {}, true);
  },

  /**
   * 标记所有通知为已读
   * @returns {Promise} Promise对象
   */
  markAllAsRead: () => {
    return patch('/notifications/mark-all-read', {}, true);
  }
};

/**
 * 反馈相关API
 */
const feedback = {
  /**
   * 提交反馈
   * @param {Object} data 反馈数据
   * @param {String} data.content 反馈内容
   * @param {String} data.type 反馈类型 (bug, suggestion, question, other)
   * @param {Array} data.images 图片URL数组，可选
   * @param {Object} data.deviceInfo 设备信息，可选
   * @returns {Promise} Promise对象
   */
  createFeedback: (data) => {
    // 参数验证
    if (!data.content || !data.content.trim()) {
      return Promise.reject(new Error('反馈内容不能为空'));
    }
    
    // 构建请求数据
    const feedbackData = {
      content: data.content.trim(),
      type: data.type || 'other',
      images: data.images || [],
      deviceInfo: data.deviceInfo || {}
    };
    
    console.log('提交反馈数据:', feedbackData);
    return post('/feedback', feedbackData, true);
  },

  /**
   * 获取用户的反馈列表
   * @param {Object} params 查询参数
   * @param {Number} params.page 页码，默认为1
   * @param {Number} params.limit 每页数量，默认为10
   * @returns {Promise} Promise对象
   */
  getUserFeedbacks: (params = {}) => {
    // 确保参数正确
    const defaultParams = {
      page: 1,
      limit: 10
    };
    
    // 合并默认参数和传入参数
    const queryParams = { ...defaultParams, ...params };
    
    // 移除空值
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete queryParams[key];
      }
    });
    
    console.log('获取用户反馈列表参数:', queryParams);
    return get('/feedback/my', queryParams, true);
  },

  /**
   * 获取反馈详情
   * @param {String} feedbackId 反馈ID
   * @returns {Promise} Promise对象
   */
  getFeedbackDetail: (feedbackId) => {
    // 参数验证
    if (!feedbackId) {
      return Promise.reject(new Error('反馈ID不能为空'));
    }
    
    return get(`/feedback/${feedbackId}`, {}, true);
  },

  /**
   * 上传反馈图片
   * @param {Array} filePaths 本地文件路径数组
   * @returns {Promise} Promise对象
   */
  uploadFeedbackImages: (filePaths) => {
    // 参数验证
    if (!filePaths || !Array.isArray(filePaths) || filePaths.length === 0) {
      return Promise.reject(new Error('请选择要上传的图片'));
    }
    
    if (filePaths.length > 6) {
      return Promise.reject(new Error('最多只能上传6张图片'));
    }
    
    console.log('上传反馈图片:', filePaths);
    return upload('/feedback/upload-images', filePaths, 'images', {}, true);
  }
};

/**
 * 产品相关API
 */
const product = {
  /**
   * 获取产品详细参数
   * @param {String} productName 产品名称，必需，需要与数据库中的skuName精确匹配
   * @returns {Promise} Promise对象
   */
  getProductParams: (productName) => {
    // 参数验证
    if (!productName || typeof productName !== 'string') {
      return Promise.reject(new Error('产品名称不能为空'));
    }
    
    const trimmedProductName = productName.trim();
    if (!trimmedProductName) {
      return Promise.reject(new Error('产品名称不能为空'));
    }
    
    if (trimmedProductName.length > 100) {
      return Promise.reject(new Error('产品名称不能超过100个字符'));
    }
    
    // 构建查询参数
    const queryParams = {
      productName: trimmedProductName
    };
    
    console.log('获取产品参数请求参数:', queryParams);
    return get('/products/params', queryParams, false); // 公开接口，不需要认证
  },

  /**
   * 搜索产品名称（用于自动完成）
   * @param {String} keyword 搜索关键词，必需
   * @param {Number} limit 返回数量限制，可选，默认为10，范围1-20
   * @param {String} category 产品类别筛选，可选，可选值: 'phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other'
   * @returns {Promise} Promise对象
   */
  searchProductNames: (keyword, limit = 10, category = '') => {
    // 参数验证
    if (!keyword || typeof keyword !== 'string') {
      return Promise.reject(new Error('搜索关键词不能为空'));
    }
    
    const trimmedKeyword = keyword.trim();
    if (!trimmedKeyword) {
      return Promise.reject(new Error('搜索关键词不能为空'));
    }
    
    if (trimmedKeyword.length > 50) {
      return Promise.reject(new Error('搜索关键词不能超过50个字符'));
    }
    
    // 验证返回数量限制
    const searchLimit = Math.min(Math.max(Number(limit) || 10, 1), 20);
    
    // 验证产品类别
    const validCategories = ['phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other', ''];
    const searchCategory = validCategories.includes(category) ? category : '';
    
    // 构建查询参数
    const queryParams = {
      keyword: trimmedKeyword,
      limit: searchLimit
    };
    
    // 添加类别筛选（如果有的话）
    if (searchCategory) {
      queryParams.category = searchCategory;
    }
    
    console.log('搜索产品名称请求参数:', queryParams);
    return get('/products/search-names', queryParams, false); // 公开接口，不需要认证
  },

  /**
   * 查询产品库产品
   * @param {String} productType 产品类型，可选，可选值: 'phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other'
   * @param {String} brandName 品牌名称，可选
   * @param {Number} page 页码，可选，默认为1
   * @param {Number} limit 每页数量，可选，默认为10，范围1-50
   * @returns {Promise} Promise对象
   */
  queryProducts: (productType = '', brandName = '', page = 1, limit = 10) => {
    // 验证产品类型
    const validProductTypes = ['phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other', ''];
    const searchProductType = validProductTypes.includes(productType) ? productType : '';
    
    // 验证品牌名称
    const searchBrandName = brandName && typeof brandName === 'string' ? brandName.trim() : '';
    
    if (searchBrandName && searchBrandName.length > 50) {
      return Promise.reject(new Error('品牌名称不能超过50个字符'));
    }
    
    // 验证分页参数
    const searchPage = Math.max(Number(page) || 1, 1);
    const searchLimit = Math.min(Math.max(Number(limit) || 10, 1), 50);
    
    // 构建查询参数
    const queryParams = {
      page: searchPage,
      limit: searchLimit
    };
    
    // 添加产品类型筛选（如果有的话）
    if (searchProductType) {
      queryParams.productType = searchProductType;
    }
    
    // 添加品牌筛选（如果有的话）
    if (searchBrandName) {
      queryParams.brandName = searchBrandName;
    }
    
    console.log('查询产品库产品请求参数:', queryParams);
    return get('/products/query', queryParams, false); // 公开接口，不需要认证
  },

  /**
   * 产品参数对比
   * @param {Array<String>} productNames 产品名称列表，支持2-5个产品对比
   * @returns {Promise} Promise对象
   */
  compareProducts: (productNames) => {
    // 参数验证
    if (!Array.isArray(productNames)) {
      return Promise.reject(new Error('产品名称必须是数组格式'));
    }
    
    if (productNames.length < 2) {
      return Promise.reject(new Error('至少需要提供2个产品进行对比'));
    }
    
    if (productNames.length > 5) {
      return Promise.reject(new Error('最多支持5个产品同时对比'));
    }
    
    // 过滤空值和空字符串
    const validProductNames = productNames.filter(name => 
      name && typeof name === 'string' && name.trim().length > 0
    );
    
    if (validProductNames.length < 2) {
      return Promise.reject(new Error('有效的产品名称至少需要2个'));
    }
    
    // 构建请求数据
    const requestData = {
      productNames: validProductNames.map(name => name.trim())
    };
    
    console.log('产品对比请求数据:', requestData);
    return post('/products/compare', requestData, false); // 公开接口，不需要认证
  },

  /**
   * 产品参数对比 V4版本 - 基于参数字段提取的智能对比
   * @param {Array<String>} productNames 产品名称列表，支持2-6个产品对比
   * @returns {Promise} Promise对象
   */
  compareProductsV4: (productNames) => {
    // 参数验证
    if (!Array.isArray(productNames)) {
      return Promise.reject(new Error('产品名称必须是数组格式'));
    }

    if (productNames.length < 2) {
      return Promise.reject(new Error('至少需要提供2个产品进行对比'));
    }

    if (productNames.length > 6) {
      return Promise.reject(new Error('最多支持6个产品同时对比'));
    }

    // 过滤空值和空字符串
    const validProductNames = productNames.filter(name =>
      name && typeof name === 'string' && name.trim().length > 0
    );

    if (validProductNames.length < 2) {
      return Promise.reject(new Error('有效的产品名称至少需要2个'));
    }

    // 验证产品名称长度
    for (const name of validProductNames) {
      if (name.trim().length > 100) {
        return Promise.reject(new Error('产品名称不能超过100个字符'));
      }
    }

    // 构建请求数据
    const requestData = {
      productNames: validProductNames.map(name => name.trim())
    };

    console.log('产品对比V4请求数据:', requestData);
    return post('/products/compare-v4', requestData, false); // 公开接口，不需要认证
  },

  /**
   * 产品参数基础对比 - 非AI版本，返回原始参数数据
   * @param {Array<String>} productNames 产品名称列表，支持2-6个产品对比
   * @returns {Promise} Promise对象
   */
  compareProductsBasic: (productNames) => {
    // 参数验证
    if (!Array.isArray(productNames)) {
      return Promise.reject(new Error('产品名称必须是数组格式'));
    }

    if (productNames.length < 2) {
      return Promise.reject(new Error('至少需要提供2个产品进行对比'));
    }

    if (productNames.length > 6) {
      return Promise.reject(new Error('最多支持6个产品同时对比'));
    }

    // 过滤空值和空字符串
    const validProductNames = productNames.filter(name =>
      name && typeof name === 'string' && name.trim().length > 0
    );

    if (validProductNames.length < 2) {
      return Promise.reject(new Error('有效的产品名称至少需要2个'));
    }

    // 验证产品名称长度
    for (const name of validProductNames) {
      if (name.trim().length > 100) {
        return Promise.reject(new Error('产品名称不能超过100个字符'));
      }
    }

    // 构建请求数据
    const requestData = {
      productNames: validProductNames.map(name => name.trim())
    };

    console.log('产品基础对比请求数据:', requestData);
    return post('/products/compare-basic', requestData, false); // 公开接口，不需要认证
  }
};

module.exports = {
  auth,
  user,
  question,
  answer,
  comment,
  result,
  notification,
  feedback,
  product
};
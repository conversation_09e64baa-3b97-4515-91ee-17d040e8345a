const productService = require('../services/productService');
const productLibraryService = require('../services/product/productLibraryService');
const { getProductParamsByName } = require('../services/product/getProductParams');
const { compareProductsByNamesV4 } = require('../services/product/productCompare');
const { compareProductsByNamesBasic } = require('../services/product/productCompareBasic');
const { success, error } = require('../utils/response');
const {
  compareProductsSchema,
  searchProductNamesSchema,
  queryProductsSchema,
  getProductParamsSchema,
  compareProductsV4Schema,
  compareProductsBasicSchema
} = require('../utils/productvalidator');

/**
 * @desc    产品参数对比
 * @route   POST /api/v1/products/compare
 * @access  Public
 */
const compareProducts = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = compareProductsSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { productNames } = value;

    // 调用服务层进行产品对比
    const result = await productService.compareProductsByNames(productNames);

    if (!result.success) {
      return error(res, 400, result.message, result.data);
    }
    // 返回成功响应
    const responseData = {
      productType: result.data.productType,
      products: result.data.products,
      comparisonTable: result.data.comparison,
      aiAnalysis: result.data.aiAnalysis,
      meta: {
        requestedProducts: productNames,
        foundProductsCount: result.data.products.length,
        notFoundProducts: result.data.notFoundProducts || []
      }
    };
    return success(res, 200, '产品对比成功', responseData);

  } catch (err) {
    console.error('产品对比控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('至少需要提供') || err.message.includes('最多支持')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('找到的可对比产品数量不足')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('只能对比相同类型的产品')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('AI')) {
      return error(res, 503, 'AI分析服务暂时不可用，请稍后重试');
    }

    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

/**
 * @desc    搜索产品名称（用于自动完成）
 * @route   GET /api/v1/products/search-names
 * @access  Public
 */
const searchProductNames = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = searchProductNamesSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { keyword, limit, category } = value;

    // 调用服务层进行产品名称搜索
    const result = await productService.searchProductNames(keyword, limit, category);

    if (!result.success) {
      return error(res, 400, result.message, result.data);
    }

    // 返回成功响应
    const responseData = {
      productNames: result.data.productNames,  // 直接返回产品名称数组
      total: result.data.total,
      searchTime: result.data.searchTime,
      meta: {
        keyword: result.data.keyword,
        requestedLimit: limit,
        category: category || 'all',
        description: '输入关键词搜索匹配的产品名称'
      }
    };

    return success(res, 200, '搜索成功', responseData);

  } catch (err) {
    console.error('产品名称搜索控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('搜索关键词')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('返回数量')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('产品类别')) {
      return error(res, 400, err.message);
    }

    return error(res, 500, '搜索服务暂时不可用，请稍后重试');
  }
};

/**
 * @desc    查询产品库产品
 * @route   GET /api/v1/products/query
 * @access  Public
 */
const queryProducts = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = queryProductsSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { productType, brandName, page, limit } = value;

    // 调用服务层进行产品库查询
    const result = await productLibraryService.queryProducts({
      productType,
      brandName,
      page,
      limit
    });

    if (!result.success) {
      return error(res, 500, result.message, result.error);
    }

    // 返回成功响应
    const responseData = {
      products: result.data.products,
      pagination: result.data.pagination,
      meta: {
        productType: productType || 'all',
        brandName: brandName || 'all',
        description: '根据产品类型和品牌查询产品库中的产品'
      }
    };

    return success(res, 200, result.message, responseData);

  } catch (err) {
    console.error('产品库查询控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('产品类型')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('品牌名称')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('页码')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('每页数量')) {
      return error(res, 400, err.message);
    }

    return error(res, 500, '产品库查询服务暂时不可用，请稍后重试');
  }
};

/**
 * @desc    获取产品参数
 * @route   GET /api/v1/products/params
 * @access  Public
 */
const getProductParams = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = getProductParamsSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { productName } = value;

    // 调用服务层获取产品参数
    const result = await getProductParamsByName(productName);

    if (!result.success) {
      // 如果是产品未找到的错误，返回404
      if (result.error && result.error.includes('未找到产品')) {
        return error(res, 404, result.error);
      }
      
      return error(res, 400, result.error);
    }

    // 返回成功响应
    const responseData = {
      product: result.data,
      meta: {
        requestedProduct: productName,
        description: '获取产品的详细参数信息，包括基本信息、价格、配置和规格参数'
      }
    };

    return success(res, 200, '获取产品参数成功', responseData);

  } catch (err) {
    console.error('产品参数获取控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('产品名称')) {
      return error(res, 400, err.message);
    }
    
    if (err.message.includes('数据库')) {
      return error(res, 503, '数据库服务暂时不可用，请稍后重试');
    }

    return error(res, 500, '获取产品参数服务暂时不可用，请稍后重试');
  }
};

/**
 * @desc    产品参数对比 V4版本 - 基于参数字段提取的智能对比
 * @route   POST /api/v1/products/compare-v4
 * @access  Public
 */
const compareProductsV4 = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = compareProductsV4Schema.validate(req.body);

    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));

      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { productNames } = value;

    // 调用服务层进行产品对比 V4
    const result = await compareProductsByNamesV4(productNames);

    if (!result.success) {
      return error(res, 400, result.error, result.data);
    }

    // 返回成功响应
    const responseData = {
      products: result.data.products,
      aiAnalysis: result.data.aiAnalysis,
      meta: {
        requestedProducts: productNames,
        foundProductsCount: result.data.products.length,
        version: 'V4',
        description: '基于参数字段提取的智能产品对比分析'
      }
    };

    console.log('产品对比成功', responseData);

    return success(res, 200, '产品对比V4成功', responseData);

  } catch (err) {
    console.error('产品对比V4控制器错误:', err);

    // 处理特定错误
    if (err.message.includes('至少需要提供') || err.message.includes('最多支持')) {
      return error(res, 400, err.message);
    }

    if (err.message.includes('找到的产品数量不足')) {
      return error(res, 400, err.message);
    }

    if (err.message.includes('不支持不同类型的产品对比')) {
      return error(res, 400, err.message);
    }

    if (err.message.includes('DeepSeek') || err.message.includes('AI')) {
      return error(res, 503, 'AI分析服务暂时不可用，请稍后重试');
    }

    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

/**
 * @desc    产品参数基础对比 - 非AI版本，返回原始参数数据
 * @route   POST /api/v1/products/compare-basic
 * @access  Public
 */
const compareProductsBasic = async (req, res) => {
  try {
    // 验证请求参数
    const { error: validationError, value } = compareProductsBasicSchema.validate(req.body);

    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));

      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { productNames } = value;

    // 调用服务层进行基础产品对比
    const result = await compareProductsByNamesBasic(productNames);

    if (!result.success) {
      return error(res, 400, result.error, result.data);
    }

    // 返回成功响应
    const responseData = {
      products: result.data.products,
      rawComparisonData: result.data.rawComparisonData,
      notFoundProducts: result.data.notFoundProducts,
      meta: {
        requestedProducts: productNames,
        foundProductsCount: result.data.products.length,
        notFoundProductsCount: result.data.notFoundProducts.length,
        version: 'Basic',
        description: '基础产品参数对比，返回原始参数数据，不包含AI分析'
      }
    };

    console.log('基础产品对比成功', {
      requestedProducts: productNames,
      foundProducts: result.data.products.length,
      notFoundProducts: result.data.notFoundProducts.length
    });

    return success(res, 200, '基础产品对比成功', responseData);

  } catch (err) {
    console.error('基础产品对比控制器错误:', err);

    // 处理特定错误
    if (err.message.includes('至少需要提供') || err.message.includes('最多支持')) {
      return error(res, 400, err.message);
    }

    if (err.message.includes('找到的产品数量不足')) {
      return error(res, 400, err.message);
    }

    if (err.message.includes('产品查找失败')) {
      return error(res, 400, err.message);
    }

    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

module.exports = {
  compareProducts,
  searchProductNames,
  queryProducts,
  getProductParams,
  compareProductsV4,
  compareProductsBasic
};
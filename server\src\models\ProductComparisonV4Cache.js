const mongoose = require('mongoose');

/**
 * 产品对比缓存模型 V4版本
 * 专门用于缓存基于参数字段提取的智能对比结果
 * 简化版本：只保存aiAnalysis内容
 */
const ProductComparisonV4CacheSchema = new mongoose.Schema(
  {
    // 缓存标识 - 对比产品的组合唯一键
    comparisonKey: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    
    // 参与对比的产品名称列表
    productNames: [{
      type: String,
      required: true
    }],
    
    // 产品基本信息（仅包含skuName和imageUrl）
    products: [{
      skuName: { type: String, required: true },
      imageUrl: { type: String, required: true }
    }],
    
    // AI结构化分析结果
    aiAnalysis: {
      productCategory: { type: String, required: true },
      isSameCategory: { type: Boolean, required: true },
      crossCategoryNote: { type: String, default: null },
      
      // 结构化报告
      structuredReport: {
        // 技术规格对比
        technicalSpecs: [{
          category: { type: String, required: true },
          items: [{
            name: { type: String, required: true },
            productValues: { type: mongoose.Schema.Types.Mixed, required: true }, // 存储产品参数值的对象
            analysis: { type: String, required: true }
          }]
        }],
        
        // 优缺点分析
        prosAndCons: [{
          productName: { type: String, required: true },
          pros: [{ type: String }],
          cons: [{ type: String }],
          overallRating: { type: String, required: true }
        }],
        
        // 使用场景推荐
        usageScenarios: [{
          scenario: { type: String, required: true },
          description: { type: String, required: true },
          recommendedProduct: { type: String, required: true },
          reason: { type: String, required: true }
        }],
        
        // 购买建议
        purchaseAdvice: {
          specificAdvice: [{
            userType: { type: String, required: true },
            recommendation: { type: String, required: true }
          }],
          importantNotes: [{ type: String }]
        }
      },
      
      aiModel: {
        type: String,
        default: 'deepseek-chat'
      }
    }
  },
  {
    timestamps: true
  }
);

// 静态方法：生成对比键（确保A和B对比与B和A对比产生相同的键）
ProductComparisonV4CacheSchema.statics.generateComparisonKey = function(productNames) {
  // 对产品名称排序以确保相同产品组合产生相同的键
  const sortedNames = [...productNames].sort();
  return `v4_comparison_${sortedNames.join('_')}_${sortedNames.length}`;
};

// 静态方法：查找缓存
ProductComparisonV4CacheSchema.statics.findByProductNames = async function(productNames) {
  const comparisonKey = this.generateComparisonKey(productNames);
  return await this.findOne({ comparisonKey: comparisonKey });
};

// 静态方法：创建缓存
ProductComparisonV4CacheSchema.statics.createCache = async function(productNames, comparisonResult) {
  const comparisonKey = this.generateComparisonKey(productNames);
  
  const cacheData = {
    comparisonKey: comparisonKey,
    productNames: productNames,
    products: comparisonResult.data.products,
    aiAnalysis: comparisonResult.data.aiAnalysis
  };
  
  // 创建新缓存
  const cache = new this(cacheData);
  return await cache.save();
};

// 实例方法：获取缓存的对比结果
ProductComparisonV4CacheSchema.methods.getCachedResult = function() {
  return {
    success: true,
    data: {
      products: this.products,
      aiAnalysis: this.aiAnalysis
    },
    cached: true,
    cacheInfo: {
      generatedAt: this.createdAt
    }
  };
};

module.exports = mongoose.model('ProductComparisonV4Cache', ProductComparisonV4CacheSchema);
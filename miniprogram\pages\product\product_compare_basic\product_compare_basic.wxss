/* pages/product/product_compare_basic/product_compare_basic.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #fff;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e3e3e3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #fff;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 对比结果 */
.compare-result {
  background: #fff;
  min-height: 100vh;
}

/* 产品头部 */
.products-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 40rpx 30rpx 30rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: bold;
}

.products-scroll {
  margin-bottom: 30rpx;
}

.products-list {
  display: flex;
  gap: 20rpx;
  padding: 0 10rpx;
}

.product-card {
  flex-shrink: 0;
  width: 200rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.product-card:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 120rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.product-name {
  font-size: 24rpx;
  line-height: 1.4;
  text-align: center;
  color: #fff;
  word-break: break-all;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
  padding: 20rpx;
  color: #fff;
  font-size: 26rpx;
  backdrop-filter: blur(10rpx);
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.25);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.btn-text {
  font-size: 26rpx;
}

/* 对比内容 */
.compare-content {
  padding: 0;
}

/* 分类section样式 */
.category-section {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin-bottom: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.category-header:active {
  background: #f0f0f0;
}

.category-header.expanded {
  background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%);
  box-shadow: 0 4rpx 12rpx rgba(25,118,210,0.2);
}

.category-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.category-toggle {
  display: flex;
  align-items: center;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.category-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

.category-content.show {
  max-height: none;
}

.category-content.hide {
  max-height: 0;
}

.spec-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #fafbfc;
  margin: 2rpx 0;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #1976d2;
  margin-bottom: 20rpx;
}

.spec-values {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.value-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.product-label {
  flex-shrink: 0;
  width: 200rpx;
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
}

.product-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

/* 数据信息 */
.data-info-section {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid #e9ecef;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.data-info-content {
  padding: 30rpx;
  background: #f8f9fa;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  flex-shrink: 0;
  width: 160rpx;
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #fff;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .products-list {
    gap: 16rpx;
  }

  .product-card {
    width: 180rpx;
    padding: 16rpx;
  }

  .product-image-container {
    height: 100rpx;
  }

  .spec-values {
    gap: 12rpx;
  }

  .value-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8rpx;
  }

  .product-label {
    width: 100%;
    margin-right: 0;
  }
}

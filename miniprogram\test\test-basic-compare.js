/**
 * 简单的基础产品对比测试脚本
 * 对比产品：华为 Mate 70 Pro 和 苹果iPhone 16 Pro
 */

// 配置后端服务器地址
const BASE_URL = 'http://localhost:3000/api/v1';

/**
 * 保存数据到JSON文件
 */
function saveToJsonFile(data) {
  const filename = 'basic-compare-result.json';
  const jsonData = JSON.stringify(data, null, 2);

  // 在小程序环境中
  if (typeof wx !== 'undefined') {
    const fs = wx.getFileSystemManager();
    const filePath = `${wx.env.USER_DATA_PATH}/${filename}`;
    fs.writeFileSync(filePath, jsonData, 'utf8');
    console.log(`✅ 数据已保存到: ${filePath}`);
  } else {
    // 浏览器环境
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log(`✅ 数据已下载: ${filename}`);
  }
}

/**
 * 发送HTTP请求
 */
function sendRequest(url, data) {
  return new Promise((resolve, reject) => {
    if (typeof wx !== 'undefined') {
      // 小程序环境
      wx.request({
        url: url,
        method: 'POST',
        data: data,
        header: { 'Content-Type': 'application/json' },
        success: (res) => resolve(res.data),
        fail: (error) => reject(error)
      });
    } else {
      // 浏览器环境
      fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      .then(response => response.json())
      .then(data => resolve(data))
      .catch(error => reject(error));
    }
  });
}

/**
 * 执行基础对比测试
 */
async function testBasicCompare() {
  console.log('🧪 开始测试基础产品对比接口...');
  console.log('📱 测试产品: 华为 Mate 70 Pro vs 苹果iPhone 16 Pro');
  console.log('🌐 后端地址:', BASE_URL);
  console.log('⏰ 测试时间:', formatTime(Date.now()));
  console.log('');

  const testProducts = ['华为 Mate 70 Pro', '苹果iPhone 16 Pro'];
  const requestData = {
    productNames: testProducts
  };

  const apiUrl = `${BASE_URL}/products/compare-basic`;
  const startTime = Date.now();

  try {
    console.log('📡 正在发送HTTP请求...');
    console.log('� 请求URL:', apiUrl);
    console.log('📤 请求数据:', JSON.stringify(requestData, null, 2));
    console.log('');

    // 发送请求
    const response = await sendRequest(apiUrl, requestData);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log('✅ 请求成功!');
    console.log(`⏱️ 响应时间: ${duration}ms`);
    console.log('');

    // 分析响应数据
    console.log('📊 响应数据分析:');
    console.log(`   - 成功状态: ${response.success}`);
    console.log(`   - 响应码: ${response.code}`);
    console.log(`   - 响应消息: ${response.message}`);

    if (response.success && response.data) {
      const { products, rawComparisonData, notFoundProducts, meta } = response.data;

      console.log(`   - 找到产品数量: ${products ? products.length : 0}`);
      console.log(`   - 未找到产品数量: ${notFoundProducts ? notFoundProducts.length : 0}`);

      if (notFoundProducts && notFoundProducts.length > 0) {
        console.log(`   - 未找到的产品: ${notFoundProducts.join(', ')}`);
      }

      if (products && products.length > 0) {
        console.log('   - 产品信息:');
        products.forEach((product, index) => {
          console.log(`     ${index + 1}. ${product.skuName} (${product.productType})`);
        });
      }

      // 分析对比数据结构
      if (rawComparisonData) {
        const analysis = analyzeComparisonData(rawComparisonData);
        console.log(`   - 参数分类数量: ${analysis.totalCategories}`);
        console.log(`   - 总参数数量: ${analysis.totalParameters}`);

        console.log('   - 分类详情:');
        Object.keys(analysis.categoriesDetail).forEach(category => {
          const detail = analysis.categoriesDetail[category];
          console.log(`     * ${category}: ${detail.parameterCount}个参数`);
        });

        console.log('   - 数据结构示例:');
        Object.keys(analysis.sampleData).slice(0, 2).forEach(category => {
          const sample = analysis.sampleData[category];
          console.log(`     * ${category}.${sample.parameterName}:`, sample.data);
        });
      }

      if (meta) {
        console.log(`   - 版本: ${meta.version}`);
        console.log(`   - 描述: ${meta.description}`);
      }
    }

    // 构建完整的测试结果
    const testResult = {
      testInfo: {
        testTime: formatTime(Date.now()),
        testProducts: testProducts,
        apiUrl: apiUrl,
        requestData: requestData,
        responseTime: duration,
        testStatus: 'SUCCESS'
      },
      response: response,
      dataAnalysis: response.success && response.data && response.data.rawComparisonData ?
        analyzeComparisonData(response.data.rawComparisonData) : null
    };

    // 保存完整数据
    const filename = `basic-compare-result-${Date.now()}.json`;
    saveToJsonFile(testResult, filename);

    console.log('');
    console.log('🎉 基础对比测试完成!');
    console.log(`📄 完整数据已保存到: ${filename}`);

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.error('❌ 测试失败:', error);
    console.log(`⏱️ 失败前耗时: ${duration}ms`);

    // 保存错误信息
    const errorResult = {
      testInfo: {
        testTime: formatTime(Date.now()),
        testProducts: testProducts,
        apiUrl: apiUrl,
        requestData: requestData,
        responseTime: duration,
        testStatus: 'ERROR'
      },
      error: {
        message: error.message || '未知错误',
        details: error
      }
    };

    saveToJsonFile(errorResult, `basic-compare-error-${Date.now()}.json`);
  }
}

/**
 * 运行测试的入口函数
 */
function runTest() {
  console.log('🚀 启动基础产品对比接口测试');
  testBasicCompare();
}

// 如果在浏览器环境中，可以直接调用
if (typeof window !== 'undefined') {
  // 添加到全局作用域，方便在控制台调用
  window.testBasicCompare = testBasicCompare;
  window.runTest = runTest;

  console.log('💡 在浏览器控制台中输入 runTest() 开始测试');
}

// 导出函数
module.exports = {
  testBasicCompare,
  runTest,
  saveToJsonFile,
  analyzeComparisonData
};

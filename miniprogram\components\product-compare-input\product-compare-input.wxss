/**
 * 产品对比输入组件样式
 * Product Compare Input Component Styles
 * 依赖：全局样式系统（variables.wxss, components.wxss, utilities.wxss）
 */

/* ==================== 组件容器 Component Container ==================== */
.compare-input-container {
  position: relative;
  z-index: 100;
}

/* ==================== 对比切换按钮 Compare Toggle Button ==================== */
.compare-toggle-btn {
  position: fixed;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #3B7ADB, #5B9BD5);
  border-radius: 50rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(59, 122, 219, 0.3);
  transition: all 0.3s ease;
  z-index: 101;
}

.compare-toggle-btn:active {
  transform: translateY(-50%) scale(0.95);
}

.compare-toggle-btn.active {
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
}

.toggle-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.toggle-text {
  font-size: 20rpx;
  color: white;
  font-weight: 500;
}

.compare-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 36rpx;
  height: 36rpx;
  background: #FF4757;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  border: 4rpx solid white;
}

/* ==================== 对比面板 Compare Panel ==================== */
.compare-panel {
  position: fixed;
  right: 0;
  top: 0;
  width: 600rpx;
  height: 100vh;
  background: white;
  box-shadow: -4rpx 0 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 102;
  display: flex;
  flex-direction: column;
}

.compare-panel.show {
  transform: translateX(0);
}

.compare-panel.hide {
  transform: translateX(100%);
}

/* ==================== 面板头部 Panel Header ==================== */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: #f8f9fa;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.title-icon {
  font-size: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.count-text {
  font-size: 24rpx;
  color: #666;
}

.panel-actions {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.clear-btn {
  background: #FFF3F3;
  border: 2rpx solid #FFE6E6;
}

.clear-btn:active {
  background: #FFE6E6;
}

.close-btn {
  background: #f0f0f0;
  border: 2rpx solid #e6e6e6;
}

.close-btn:active {
  background: #e6e6e6;
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* ==================== 对比产品列表 Compare Products List ==================== */
.compare-products-scroll {
  flex: 1;
  padding: 20rpx;
}

.compare-products-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.compare-product-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.compare-product-item:active {
  transform: translateY(2rpx);
  border-color: #3B7ADB;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.15);
}

.product-image-container {
  position: relative;
  margin-right: 20rpx;
}

.product-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  background: #f0f0f0;
  border: 2rpx solid #e6e6e6;
}

.remove-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #FF4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

.remove-icon {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.product-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* ==================== 空槽位 Empty Slot ==================== */
.empty-slot {
  border: 2rpx dashed #d0d0d0;
  background: #fafafa;
  justify-content: center;
  min-height: 140rpx;
}

.empty-slot-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.empty-icon {
  font-size: 40rpx;
  color: #ccc;
}

.empty-text {
  font-size: 24rpx;
  color: #999;
}

/* ==================== 面板底部 Panel Footer ==================== */
.panel-footer {
  padding: 30rpx 40rpx;
  border-top: 2rpx solid #f0f0f0;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.compare-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.compare-btn.enabled {
  background: linear-gradient(135deg, #3B7ADB, #5B9BD5);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.3);
}

.compare-btn.enabled:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(59, 122, 219, 0.3);
}

.compare-btn.disabled {
  background: #f0f0f0;
  color: #ccc;
  cursor: not-allowed;
}

/* V4对比按钮样式 */
.compare-btn-v4 {
  width: 100%;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.compare-btn-v4.enabled {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.compare-btn-v4.enabled:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.compare-btn-v4.disabled {
  background: #f0f0f0;
  color: #ccc;
  cursor: not-allowed;
}

.compare-btn-v4 .btn-icon {
  font-size: 32rpx;
}

.compare-btn-v4 .btn-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* ==================== 空状态 Empty State ==================== */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;
}

.empty-state .empty-icon {
  font-size: 100rpx;
  color: #ddd;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* ==================== 遮罩层 Mask ==================== */
.compare-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  transition: opacity 0.3s ease;
  z-index: 100;
}

.compare-mask.show {
  opacity: 1;
  pointer-events: auto;
}

.compare-mask.hide {
  opacity: 0;
  pointer-events: none;
}

/* ==================== 响应式适配 Responsive Design ==================== */
@media (max-width: 750rpx) {
  .compare-panel {
    width: 100vw;
  }
  
  .compare-toggle-btn {
    right: 20rpx;
    width: 80rpx;
    height: 80rpx;
  }
  
  .toggle-icon {
    font-size: 28rpx;
  }
  
  .toggle-text {
    font-size: 18rpx;
  }
}
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    questionInfo: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    productType: '',
    selectedBrands: [],
    loading: false,
    canConfirm: false,
    availableBrands: [],
    brandStatus: {}, // 品牌选中状态追踪对象
    
    // 品牌数据
    phoneBrands: ['苹果', '华为', '小米', 'OPPO', 'VIVO', '荣耀', '一加',  '三星'],
    laptopBrands: ['苹果', '华为', '小米', '联想', '戴尔', '惠普', '华硕', '宏碁', '微软', '机械革命'],
    
    // 异步处理相关
    processing: false,
    taskId: null,
    pollInterval: null,
    processingMessage: '正在为您智能推荐产品...'
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化
    },
    detached() {
      // 组件销毁时清理定时器
      if (this.data.pollInterval) {
        clearInterval(this.data.pollInterval);
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 选择产品类型
     */
    onSelectProductType(e) {
      const { type } = e.currentTarget.dataset;
      const availableBrands = type === 'phone' ? this.data.phoneBrands : this.data.laptopBrands;
      
      // 初始化品牌状态对象
      const brandStatus = {};
      availableBrands.forEach(brand => {
        brandStatus[brand] = false;
      });
      
      this.setData({
        productType: type,
        selectedBrands: [],
        availableBrands,
        brandStatus
      });
      
      // 更新确认按钮状态
      this.updateCanConfirm();
    },

    /**
     * 切换品牌选择
     */
    onToggleBrand(e) {
      const { brand } = e.currentTarget.dataset;
      const { selectedBrands, brandStatus } = this.data;
      
      // 切换品牌选中状态
      if (brandStatus[brand]) {
        // 取消选择
        const newSelectedBrands = selectedBrands.filter(b => b !== brand);
        const newBrandStatus = { ...brandStatus };
        newBrandStatus[brand] = false;
        
        this.setData({
          selectedBrands: newSelectedBrands,
          brandStatus: newBrandStatus
        });
      } else {
        // 添加选择，最多选择10个品牌
        if (selectedBrands.length >= 10) {
          wx.showToast({
            title: '最多选择10个品牌',
            icon: 'none'
          });
          return;
        }
        
        const newSelectedBrands = [...selectedBrands, brand];
        const newBrandStatus = { ...brandStatus };
        newBrandStatus[brand] = true;
        
        this.setData({
          selectedBrands: newSelectedBrands,
          brandStatus: newBrandStatus
        });
      }
      
      // 更新确认按钮状态
      this.updateCanConfirm();
    },



    /**
     * 确认选择并调用AI推荐
     */
    async onConfirm() {
      const { productType, selectedBrands, loading, processing } = this.data;
      const { questionInfo } = this.properties;
      
      if (loading || processing) return;
      
      // 验证输入
      if (!productType) {
        wx.showToast({
          title: '请选择产品类型',
          icon: 'none'
        });
        return;
      }
      
      if (selectedBrands.length === 0) {
        wx.showToast({
          title: '请选择至少一个品牌',
          icon: 'none'
        });
        return;
      }
      
      // 验证问题信息
      if (!questionInfo.title || !questionInfo.title.trim()) {
        wx.showToast({
          title: '请先填写问题标题',
          icon: 'none'
        });
        return;
      }
      
      // 构建推荐参数  
      const filterOptions = {
        productType,
        brands: selectedBrands,
        budget: questionInfo.budget || null
      };
      
      console.log('AI推荐参数:', { questionInfo, filterOptions });
      
      // 设置加载状态
      this.setData({
        loading: true,
        processing: false
      });
      
      try {
        // 调用异步AI推荐接口
        const { question } = require('../../utils/api');
        const response = await question.aiRecommendProducts(questionInfo, filterOptions, true);
        
        console.log('AI推荐初始响应:', response);
        
        if (response.success) {
          // 检查是否是异步处理响应
          if (response.code === 202 && response.data.status === 'processing') {
            console.log('AI推荐正在异步处理中，开始轮询...');
            
            // 设置处理中状态
            this.setData({
              loading: false,
              processing: true,
              taskId: response.data.taskId,
              processingMessage: response.data.message || '正在为您智能推荐产品...'
            });
            
            // 开始轮询检查结果状态
            this.startPollingRecommendStatus();
          } else {
            // 同步处理完成
            this.handleRecommendSuccess(response.data);
          }
        } else {
          throw new Error(response.message || 'AI推荐失败');
        }
      } catch (error) {
        console.error('AI推荐失败:', error);
        this.setData({
          loading: false,
          processing: false
        });
        
        wx.showToast({
          title: error.message || 'AI推荐失败，请重试',
          icon: 'none'
        });
      }
    },

    /**
     * 开始轮询检查推荐状态
     */
    startPollingRecommendStatus() {
      // 清除可能存在的旧定时器
      if (this.data.pollInterval) {
        clearInterval(this.data.pollInterval);
      }
      
      // 创建新的轮询定时器
      const pollInterval = setInterval(() => {
        this.checkRecommendStatus();
      }, 3000); // 每3秒检查一次状态
      
      // 保存定时器ID
      this.setData({
        pollInterval: pollInterval
      });
    },

    /**
     * 检查推荐状态
     */
    async checkRecommendStatus() {
      const { taskId } = this.data;
      
      if (!taskId) {
        console.error('任务ID不存在');
        this.stopPolling();
        return;
      }
      
      try {
        // 调用API检查状态
        const { question } = require('../../utils/api');
        const response = await question.checkAIRecommendStatus(taskId);
        
        if (response.success) {
          const statusData = response.data;
          console.log('推荐状态检查结果:', statusData);
          
          // 如果推荐已生成完成
          if (statusData.status === 'completed') {
            console.log('AI推荐已生成完成');
            
            // 停止轮询
            this.stopPolling();
            
            // 处理推荐结果
            this.handleRecommendSuccess(statusData.result.data);
          } 
          // 如果推荐失败
          else if (statusData.status === 'failed') {
            console.error('AI推荐失败:', statusData.error);
            
            // 停止轮询
            this.stopPolling();
            
            this.setData({
              processing: false
            });
            
            wx.showToast({
              title: statusData.error || 'AI推荐失败，请重试',
              icon: 'none'
            });
          }
          else {
            // 仍在处理中，更新处理时间
            const processingTime = statusData.processingTime || 0;
            const seconds = Math.floor(processingTime / 1000);
            
            this.setData({
              processingMessage: `正在为您智能推荐产品... (${seconds}秒)`
            });
            
            console.log('AI推荐仍在处理中...', `已耗时: ${seconds}秒`);
          }
        } else {
          console.error('检查推荐状态失败:', response.message);
        }
      } catch (error) {
        console.error('检查推荐状态异常:', error.message);
      }
    },

    /**
     * 停止轮询
     */
    stopPolling() {
      if (this.data.pollInterval) {
        clearInterval(this.data.pollInterval);
        this.setData({
          pollInterval: null
        });
      }
    },

    /**
     * 处理推荐成功
     */
    handleRecommendSuccess(data) {
      console.log('AI推荐成功:', data);
      
      this.setData({
        loading: false,
        processing: false
      });
      
      // 触发父组件的推荐成功事件
      this.triggerEvent('recommend', {
        success: true,
        data: data
      });
      
      wx.showToast({
        title: 'AI推荐完成',
        icon: 'success'
      });
    },

    /**
     * 关闭弹窗
     */
    onClose() {
      // 清理轮询定时器
      this.stopPolling();
      
      // 重置状态
      this.setData({
        loading: false,
        processing: false,
        taskId: null
      });
      
      this.triggerEvent('close');
    },

    /**
     * 设置加载状态
     */
    setLoading(loading) {
      this.setData({ loading });
    },

    /**
     * 重置表单数据
     */
    resetForm() {
      this.setData({
        productType: '',
        selectedBrands: [],
        loading: false,
        canConfirm: false,
        availableBrands: [],
        brandStatus: {}
      });
    },

    /**
     * 更新确认按钮状态
     */
    updateCanConfirm() {
      const { productType, selectedBrands } = this.data;
      const canConfirm = productType && selectedBrands.length > 0;
      this.setData({ canConfirm });
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'show': function(show) {
      if (!show) {
        // 弹窗关闭时重置表单
        this.resetForm();
      }
    }
  }
}); 